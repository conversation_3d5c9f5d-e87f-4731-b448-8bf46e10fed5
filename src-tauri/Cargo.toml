[package]
name = "colony-app"
version = "1.1.4"
description = "A GUI frontend to the Autonomi network with a client side search engine"
authors = ["<PERSON> McClish", "Max<PERSON> Rodriguez"]
edition = "2021"
homepage = "https://github.com/zettawatt/colony"
license = "GPL-3.0-only"

[[bin]]
name = "colony-app"
path = "src/main.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }
ureq = { version = "2", features = ["json"], default-features = false }
serde_json = "1"

[dependencies]
tauri = { version = "2", features = ["rustls-tls"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-notification = "2"
colonylib = "0.5.13"
autonomi = "0.5.3"
tracing = "0.1.41"
tokio = { version = "1", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
tracing-subscriber = "0.3.18"
thiserror = "2.0.12"
tauri-plugin-dialog = "2"
tauri-plugin-store = "2"
tauri-plugin-shell = "2"
clap = "4.5.40"
tauri-plugin-log = "2"
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }

[profile.dev]
opt-level = 0
incremental = true

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[dev-dependencies]
tokio-test = "0.4"
ruint = "1.12.3"
tracing-subscriber = "0.3.18"
futures = "0.3"



# Android-specific configuration
# Note: oxrocksdb has cross-compilation issues on Android
# Solution: Override oxigraph to use memory-only backend to avoid RocksDB
[target.'cfg(target_os = "android")'.dependencies]
# Use rustls instead of native-tls for Android to avoid OpenSSL issues
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
# Override oxigraph to use memory-only backend for Android (no RocksDB)
oxigraph = { version = "0.4", default-features = false }

# Android build notes:
# 1. Install Android NDK: https://developer.android.com/ndk/downloads
# 2. Set ANDROID_NDK_ROOT environment variable
# 3. Install Android SDK and set ANDROID_HOME environment variable
# 4. The OpenSSL issue has been resolved by using rustls-tls instead of native-tls
# 5. The oxrocksdb issue has been resolved by using memory-only storage backend


