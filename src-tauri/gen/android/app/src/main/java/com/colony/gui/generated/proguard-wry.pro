# THIS FILE IS AUTO-GENERATED. DO NOT MODIFY!!

# Copyright 2020-2023 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

-keep class com.colony.gui.* {
  native <methods>;
}

-keep class com.colony.gui.WryActivity {
  public <init>(...);

  void setWebView(com.colony.gui.RustWebView);
  java.lang.Class getAppClass(...);
  java.lang.String getVersion();
}

-keep class com.colony.gui.Ipc {
  public <init>(...);

  @android.webkit.JavascriptInterface public <methods>;
}

-keep class com.colony.gui.RustWebView {
  public <init>(...);

  void loadUrlMainThread(...);
  void loadHTMLMainThread(...);
  void evalScript(...);
}

-keep class com.colony.gui.RustWebChromeClient,com.colony.gui.RustWebViewClient {
  public <init>(...);
}
