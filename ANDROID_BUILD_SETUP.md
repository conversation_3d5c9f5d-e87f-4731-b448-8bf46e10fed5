# Android Build Setup for Colony

This document provides a comprehensive guide to resolve the OpenSSL compilation issues and set up Android development for the Colony project.

## Problem Solved: OpenSSL Cross-Compilation Issue

The original error was caused by dependencies trying to use OpenSSL (via `native-tls`) for Android builds, which requires complex cross-compilation setup. We've resolved this by:

### 1. Configured Rust Dependencies to Use rustls

**Changes made to `src-tauri/Cargo.toml`:**

```toml
# Main reqwest dependency now uses rustls instead of native-tls
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }

# Build dependency also uses rustls
ureq = { version = "2", features = ["json"], default-features = false }

# Android-specific override to ensure rustls usage
[target.'cfg(target_os = "android")'.dependencies]
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
```

### 2. Benefits of This Solution

- **No OpenSSL dependency**: Eliminates the need for OpenSSL cross-compilation
- **Pure Rust TLS**: Uses `rustls` which is a pure Rust TLS implementation
- **Better Android compatibility**: Avoids native library linking issues
- **Consistent across platforms**: Same TLS implementation on all targets

## Next Steps: Android Development Environment Setup

To complete the Android build setup, you need to install the Android development tools:

### 1. Install Android NDK (Native Development Kit)

The NDK provides the cross-compilation toolchain for building native code for Android.

**Option A: Using Android Studio**
1. Install Android Studio
2. Go to Tools → SDK Manager → SDK Tools
3. Check "NDK (Side by side)" and install

**Option B: Command Line**
1. Download NDK from: https://developer.android.com/ndk/downloads
2. Extract to a directory (e.g., `~/Android/ndk`)
3. Set environment variable: `export ANDROID_NDK_ROOT=~/Android/ndk/[version]`

### 2. Install Android SDK

**Option A: Using Android Studio**
1. Android Studio automatically installs the SDK
2. Note the SDK location (usually `~/Android/Sdk`)

**Option B: Command Line Tools**
1. Download command line tools from: https://developer.android.com/studio#command-tools
2. Extract and set `ANDROID_HOME` environment variable

### 3. Set Environment Variables

Add these to your shell profile (`.bashrc`, `.zshrc`, etc.):

```bash
export ANDROID_HOME=~/Android/Sdk
export ANDROID_NDK_ROOT=~/Android/ndk/[version]
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 4. Install Required Android Components

Using `sdkmanager`:

```bash
sdkmanager "platforms;android-34"
sdkmanager "build-tools;34.0.0"
sdkmanager "ndk;[latest-version]"
```

### 5. Configure Rust for Android

The Android targets should already be installed, but verify:

```bash
rustup target list | grep android
# Should show installed targets like aarch64-linux-android
```

## Testing the Build

Once the Android development environment is set up:

```bash
# Test Rust compilation for Android
cargo build --target aarch64-linux-android

# Full Tauri Android development
cargo tauri android dev --open
```

## Troubleshooting

### If you still get OpenSSL errors:
- Verify that `default-features = false` is set for reqwest
- Check that no other dependencies are pulling in `native-tls`
- Use `cargo tree` to inspect dependency tree

### If you get NDK/toolchain errors:
- Verify `ANDROID_NDK_ROOT` points to correct NDK version
- Ensure NDK version is compatible with your Rust toolchain
- Try different NDK versions if needed

### If you get linking errors:
- Check that all required Android SDK components are installed
- Verify target architecture matches your test device/emulator

## Additional Notes

- The `oxrocksdb` crate mentioned in comments may still have Android compatibility issues
- Consider using alternative storage backends if database-related compilation fails
- The dweb sidecar binary will need special handling for Android (placeholder files)

## Success Indicators

You'll know the setup is working when:
1. `cargo build --target aarch64-linux-android` completes without OpenSSL errors
2. `cargo tauri android dev` starts the development process
3. The app builds and runs on Android device/emulator
