The goal is to make this desktop app compatible with mobile phones, specifically iOS and Android devices. The underlying Tauri 2.0 infrastructure is capable of running on these systems but the GUI does not scale to this size. I would like to make the following changes when the screen shrinks in the X dimension smaller than the current minimum size. These changes should only occur when the screen resolution shrinks below this threshold, otherwise the existing behavior is left alone:

- Header and footer behavior
  - I want the search, status, file management, wallet, and configuration buttons to move to the bottom of the screen in a single row of 5 buttons, as is typical in iOS and some Android applications. No text on these, only the icons.
  - The Colony logo and button as well as the dark/light mode switcher I want to keep at the top of the screen
  - Remove the current footer at this resolutio, i.e. the one that has the github logo and 'thank you for using colony' text.
- Search page
  - Leave the search bar at the top of the screen where it is, just below the header, with the Browse/Search button to the right of the search bar
  - The Tabulator is not designed to work at resolutions of this scale. I want to display some of the information, but it will need to be truncated to get everything to fit. Ideally, the user would see the far left icon, Name, Type, and Size fields. When the user clicks on an item, it will display the File Metadata dialog as it does today. I do not, under any circumstances, want a left right scroll bar.
- Status page
  - The 'Transfer Status' text can be deleted at this resolution
  - Leave the 2 existing clear buttons at the top
  - Like the tabulator on the search page, the tabulator in the status page needs to be truncated. Display the status icon, Name, and Type columns
- File Management page
  - The 'Your Pods', 'Uploads', and 'Downloads' buttons on the left I would like to have placed in a 'hamburger' component
  - The 'Your Pods', 'Uploads', and 'Downloads' plain text can be removed at this resoultion
  - On the 'Your Pods' sub page:
    - change the button text to make it easier for them to fit on the screen in one row:
      - the Sync Pods button can be changed to 'Sync'
      - the Upload All Pods button can be changed to 'Upload Pods'
      - the Create New Pod button can be changed to 'Create Pod'
    - In the table some columns will need to be hidden to fit on the screen. Show the Pod Name, Pod Address, and Operations columns
  - On the 'Uploads' sub page:
    - In the table some columns will need to be hidden to fit on the screen. Show the Name, Upload Address, and Size
  - On the 'Downloads' sub page:
    - In the table some columns will need to be hidden to fit on the screen. Show the Name, From Address, and Size
- Wallets page:
  - The 'Wallets' text can be hidden at this resolution
  - At the top of the screen, show the active wallet ETH and AUTONOMI balance information as text fields with ETH on top of AUTONOMI
  - Change the Add New Wallet button to 'Add Wallet' and center it on the screen below the active wallet balance information
  - In the table some columns will need to be hidden to fit on the screen. Show the Wallet Name, Wallet Key, and Operations columns
- Configuration page:
  - The Configuration Settings' text can be hidden at this resolution
  - There are currently 2 horizontally displayed settings columns. Combine these into one column with the column on the left on top and the column on the right on the bottom. This screen can be scrolled vertically to get to all of the content.

In summary, the changes described above are to allow this application to be displayed on a standard smart phone screen. The result should be clean and easy to use with no horizontal scrolling.
