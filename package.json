{"name": "colony-app", "version": "1.1.4", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri"}, "license": "MIT", "dependencies": {"@scure/bip39": "^1.6.0", "@tailwindcss/vite": "^4.1.6", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-log": "^2.6.0", "@tauri-apps/plugin-notification": "^2.2.2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-store": "^2.2.0", "@types/qrcode": "^1.5.5", "daisyui": "^5.0.35", "luxon": "^3.6.1", "qrcode": "^1.5.4", "sortablejs": "^1.15.6", "tabulator-tables": "^6.3.1", "tailwindcss": "^4.1.6", "uuid": "^11.1.0"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2", "@types/luxon": "^3.6.2", "@types/sortablejs": "^1.15.8", "@types/tabulator-tables": "^6.2.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "~5.6.2", "vite": "^6.0.3"}}